export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string | null
          full_name: string | null
          avatar_url: string | null
          username: string | null
          bio: string | null
          website: string | null
          location: string | null
          provider: string | null
          provider_id: string | null
          metadata: J<PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          username?: string | null
          bio?: string | null
          website?: string | null
          location?: string | null
          provider?: string | null
          provider_id?: string | null
          metadata?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          username?: string | null
          bio?: string | null
          website?: string | null
          location?: string | null
          provider?: string | null
          provider_id?: string | null
          metadata?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      error_reports: {
        Row: {
          id: string
          user_id: string | null
          error_name: string
          error_message: string
          error_stack: string | null
          context_url: string
          context_user_agent: string
          context_timestamp: number
          context_component: string | null
          context_action: string | null
          context_metadata: Json
          severity: 'low' | 'medium' | 'high' | 'critical'
          created_at: string
        }
        Insert: {
          id: string
          user_id?: string | null
          error_name: string
          error_message: string
          error_stack?: string | null
          context_url: string
          context_user_agent: string
          context_timestamp: number
          context_component?: string | null
          context_action?: string | null
          context_metadata?: Json
          severity: 'low' | 'medium' | 'high' | 'critical'
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          error_name?: string
          error_message?: string
          error_stack?: string | null
          context_url?: string
          context_user_agent?: string
          context_timestamp?: number
          context_component?: string | null
          context_action?: string | null
          context_metadata?: Json
          severity?: 'low' | 'medium' | 'high' | 'critical'
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "error_reports_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      performance_metrics: {
        Row: {
          id: string
          user_id: string | null
          metric_name: string
          metric_value: number
          metric_unit: string
          context_url: string
          context_timestamp: number
          context_component: string | null
          context_metadata: Json
          created_at: string
        }
        Insert: {
          id: string
          user_id?: string | null
          metric_name: string
          metric_value: number
          metric_unit: string
          context_url: string
          context_timestamp: number
          context_component?: string | null
          context_metadata?: Json
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          metric_name?: string
          metric_value?: number
          metric_unit?: string
          context_url?: string
          context_timestamp?: number
          context_component?: string | null
          context_metadata?: Json
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "performance_metrics_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_actions: {
        Row: {
          id: string
          user_id: string | null
          action_type: string
          action_target: string
          action_data: Json
          context_url: string
          context_timestamp: number
          session_id: string
          created_at: string
        }
        Insert: {
          id: string
          user_id?: string | null
          action_type: string
          action_target: string
          action_data?: Json
          context_url: string
          context_timestamp: number
          session_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          action_type?: string
          action_target?: string
          action_data?: Json
          context_url?: string
          context_timestamp?: number
          session_id?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_actions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_broker_configs: {
        Row: {
          id: string
          user_id: string
          broker_id: string
          config: Json
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          broker_id: string
          config: Json
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          broker_id?: string
          config?: Json
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_broker_configs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
