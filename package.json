{"name": "chrome", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:optimized": "node scripts/optimize-build.js", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "perf": "npm run build && npm run start", "perf:test": "node scripts/performance-test.js", "lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "bundle-analyzer": "npx @next/bundle-analyzer", "optimize": "node scripts/optimize-build.js"}, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/commands": "^6.8.1", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/language": "^6.11.0", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.8", "@lezer/highlight": "^1.2.1", "@next/bundle-analyzer": "^15.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tabler/icons-react": "^3.33.0", "@tanstack/react-query": "^5.77.2", "@tanstack/react-query-devtools": "^5.77.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "dotenv": "^16.5.0", "dotted-map": "^2.2.3", "framer-motion": "^12.12.2", "lightweight-charts": "^5.0.7", "lucide-react": "^0.511.0", "motion": "^12.14.0", "next": "^15.3.2", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "workbox-webpack-plugin": "^7.3.0", "zustand": "^5.0.2"}, "overrides": {"@codemirror/view": "^6.36.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.48.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^2.1.8", "eslint": "^9", "eslint-config-next": "15.1.8", "jsdom": "^25.0.1", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.1", "typescript": "^5", "vitest": "^2.1.8", "web-vitals": "^4.2.4"}}