import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TradingChart } from '@/components/charts/trading-chart';

// Mock chart data
const mockChartData = [
  {
    time: 1640995200000, // 2022-01-01
    open: 100,
    high: 105,
    low: 95,
    close: 102,
    volume: 1000,
  },
  {
    time: 1640998800000, // 2022-01-01 + 1 hour
    open: 102,
    high: 108,
    low: 100,
    close: 106,
    volume: 1200,
  },
];

// Mock the chart data query
vi.mock('@/lib/query-client', () => ({
  useChartDataQuery: vi.fn(() => ({
    data: mockChartData,
    isLoading: false,
    error: null,
  })),
}));

// Mock performance wrapper
vi.mock('@/lib/performance', () => ({
  PerformanceWrapper: ({ children }: { children: React.ReactNode }) => children,
}));

describe('TradingChart', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    vi.clearAllMocks();
  });

  const renderChart = (props = {}) => {
    const defaultProps = {
      isCrosshairMode: false,
      symbol: 'NIFTY',
      timeframe: '1H',
      appliedIndicators: [],
    };

    return render(
      <QueryClientProvider client={queryClient}>
        <TradingChart {...defaultProps} {...props} />
      </QueryClientProvider>
    );
  };

  it('renders chart container', () => {
    renderChart();
    
    const chartContainer = screen.getByRole('generic');
    expect(chartContainer).toBeInTheDocument();
  });

  it('shows loading state when data is loading', () => {
    const { useChartDataQuery } = require('@/lib/query-client');
    useChartDataQuery.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    });

    renderChart();
    
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('shows error state when there is an error', () => {
    const { useChartDataQuery } = require('@/lib/query-client');
    useChartDataQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: new Error('Failed to load data'),
    });

    renderChart();
    
    expect(screen.getByText(/failed to load chart data/i)).toBeInTheDocument();
  });

  it('creates chart with correct configuration', async () => {
    const { createChart } = require('lightweight-charts');
    
    renderChart();
    
    await waitFor(() => {
      expect(createChart).toHaveBeenCalledWith(
        expect.any(HTMLElement),
        expect.objectContaining({
          layout: expect.objectContaining({
            background: { type: 'solid', color: '#000000' },
            textColor: '#d4d4d8',
          }),
          grid: expect.objectContaining({
            vertLines: expect.objectContaining({
              color: '#27272a',
            }),
            horzLines: expect.objectContaining({
              color: '#27272a',
            }),
          }),
        })
      );
    });
  });

  it('adds candlestick series with correct styling', async () => {
    const mockChart = {
      addSeries: vi.fn(() => ({
        setData: vi.fn(),
      })),
      applyOptions: vi.fn(),
      remove: vi.fn(),
      removeSeries: vi.fn(),
    };
    
    const { createChart, CandlestickSeries } = require('lightweight-charts');
    createChart.mockReturnValue(mockChart);
    
    renderChart();
    
    await waitFor(() => {
      expect(mockChart.addSeries).toHaveBeenCalledWith(
        CandlestickSeries,
        expect.objectContaining({
          upColor: '#22c55e',
          downColor: '#ef4444',
          borderDownColor: '#ef4444',
          borderUpColor: '#22c55e',
          wickDownColor: '#ef4444',
          wickUpColor: '#22c55e',
        })
      );
    });
  });

  it('sets chart data correctly', async () => {
    const mockSeries = {
      setData: vi.fn(),
    };
    
    const mockChart = {
      addSeries: vi.fn(() => mockSeries),
      applyOptions: vi.fn(),
      remove: vi.fn(),
      removeSeries: vi.fn(),
    };
    
    const { createChart } = require('lightweight-charts');
    createChart.mockReturnValue(mockChart);
    
    renderChart();
    
    await waitFor(() => {
      expect(mockSeries.setData).toHaveBeenCalledWith([
        {
          time: 1640995200, // Converted to seconds
          open: 100,
          high: 105,
          low: 95,
          close: 102,
        },
        {
          time: 1640998800, // Converted to seconds
          open: 102,
          high: 108,
          low: 100,
          close: 106,
        },
      ]);
    });
  });

  it('updates crosshair visibility when mode changes', async () => {
    const mockChart = {
      addSeries: vi.fn(() => ({
        setData: vi.fn(),
      })),
      applyOptions: vi.fn(),
      remove: vi.fn(),
      removeSeries: vi.fn(),
    };
    
    const { createChart } = require('lightweight-charts');
    createChart.mockReturnValue(mockChart);
    
    const { rerender } = renderChart({ isCrosshairMode: false });
    
    // Change crosshair mode
    rerender(
      <QueryClientProvider client={queryClient}>
        <TradingChart
          isCrosshairMode={true}
          symbol="NIFTY"
          timeframe="1H"
          appliedIndicators={[]}
        />
      </QueryClientProvider>
    );
    
    await waitFor(() => {
      expect(mockChart.applyOptions).toHaveBeenCalledWith(
        expect.objectContaining({
          crosshair: expect.objectContaining({
            vertLine: expect.objectContaining({
              visible: false,
            }),
            horzLine: expect.objectContaining({
              visible: false,
            }),
          }),
        })
      );
    });
  });

  it('handles indicators correctly', async () => {
    const mockIndicatorSeries = {
      setData: vi.fn(),
    };
    
    const mockChart = {
      addSeries: vi.fn()
        .mockReturnValueOnce({ setData: vi.fn() }) // Candlestick series
        .mockReturnValueOnce(mockIndicatorSeries), // Indicator series
      applyOptions: vi.fn(),
      remove: vi.fn(),
      removeSeries: vi.fn(),
    };
    
    const { createChart, LineSeries } = require('lightweight-charts');
    createChart.mockReturnValue(mockChart);
    
    const appliedIndicators = [
      {
        id: 'sma_20_1234567890',
        name: 'Simple Moving Average',
        shortName: 'SMA(20)',
        color: '#ff0000',
        visible: true,
        parameters: { period: 20 },
      },
    ];
    
    renderChart({ appliedIndicators });
    
    await waitFor(() => {
      expect(mockChart.addSeries).toHaveBeenCalledWith(
        LineSeries,
        expect.objectContaining({
          color: '#ff0000',
          lineWidth: 2,
          title: 'SMA(20)',
        })
      );
    });
  });

  it('cleans up chart on unmount', async () => {
    const mockChart = {
      addSeries: vi.fn(() => ({
        setData: vi.fn(),
      })),
      applyOptions: vi.fn(),
      remove: vi.fn(),
      removeSeries: vi.fn(),
    };
    
    const { createChart } = require('lightweight-charts');
    createChart.mockReturnValue(mockChart);
    
    const { unmount } = renderChart();
    
    unmount();
    
    await waitFor(() => {
      expect(mockChart.remove).toHaveBeenCalled();
    });
  });

  it('handles resize events', async () => {
    const mockChart = {
      addSeries: vi.fn(() => ({
        setData: vi.fn(),
      })),
      applyOptions: vi.fn(),
      remove: vi.fn(),
      removeSeries: vi.fn(),
    };
    
    const { createChart } = require('lightweight-charts');
    createChart.mockReturnValue(mockChart);
    
    renderChart();
    
    // Simulate resize
    const resizeCallback = global.ResizeObserver.mock.calls[0][0];
    resizeCallback([
      {
        target: document.createElement('div'),
        contentRect: { width: 800, height: 600 },
      },
    ]);
    
    await waitFor(() => {
      expect(mockChart.applyOptions).toHaveBeenCalledWith(
        expect.objectContaining({
          width: expect.any(Number),
          height: expect.any(Number),
        })
      );
    });
  });
});
