import { createClient } from './server';

// Broker configuration interfaces
export interface BrokerConfig {
  id: string;
  name: string;
  apiUrl: string;
  wsUrl?: string;
  apiKey?: string;
  secretKey?: string;
  sandbox?: boolean;
}

export interface OrderRequest {
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit' | 'stop' | 'stop_limit';
  quantity: number;
  price?: number;
  stopPrice?: number;
  timeInForce?: 'GTC' | 'IOC' | 'FOK' | 'DAY';
  clientOrderId?: string;
}

export interface Order {
  id: string;
  clientOrderId?: string;
  symbol: string;
  side: 'buy' | 'sell';
  type: string;
  quantity: number;
  price?: number;
  status: 'pending' | 'filled' | 'partially_filled' | 'cancelled' | 'rejected';
  filledQuantity: number;
  averagePrice?: number;
  timestamp: number;
  updatedAt: number;
}

export interface Position {
  symbol: string;
  side: 'long' | 'short';
  quantity: number;
  averagePrice: number;
  unrealizedPnl: number;
  realizedPnl: number;
  timestamp: number;
}

export interface Balance {
  currency: string;
  available: number;
  locked: number;
  total: number;
}

// Abstract broker interface
export abstract class BrokerClient {
  protected config: BrokerConfig;
  protected retryAttempts = 3;
  protected retryDelay = 1000;

  constructor(config: BrokerConfig) {
    this.config = config;
  }

  // Abstract methods that each broker must implement
  abstract connect(): Promise<void>;
  abstract disconnect(): Promise<void>;
  abstract placeOrder(order: OrderRequest): Promise<Order>;
  abstract cancelOrder(orderId: string): Promise<boolean>;
  abstract getOrder(orderId: string): Promise<Order>;
  abstract getOrders(symbol?: string): Promise<Order[]>;
  abstract getPositions(): Promise<Position[]>;
  abstract getBalances(): Promise<Balance[]>;
  abstract subscribeToOrderUpdates(callback: (order: Order) => void): () => void;
  abstract subscribeToPositionUpdates(callback: (position: Position) => void): () => void;

  // Common retry logic
  protected async withRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === this.retryAttempts) {
          throw lastError;
        }
        
        const delay = this.retryDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }

  // Common validation
  protected validateOrder(order: OrderRequest): void {
    if (!order.symbol || !order.side || !order.type || !order.quantity) {
      throw new Error('Missing required order fields');
    }
    
    if (order.quantity <= 0) {
      throw new Error('Order quantity must be positive');
    }
    
    if ((order.type === 'limit' || order.type === 'stop_limit') && !order.price) {
      throw new Error('Limit orders require a price');
    }
    
    if ((order.type === 'stop' || order.type === 'stop_limit') && !order.stopPrice) {
      throw new Error('Stop orders require a stop price');
    }
  }
}

// Example implementation for a hypothetical broker
export class AlpacaBrokerClient extends BrokerClient {
  private ws: WebSocket | null = null;
  private orderCallbacks: Set<(order: Order) => void> = new Set();
  private positionCallbacks: Set<(position: Position) => void> = new Set();

  async connect(): Promise<void> {
    // Implementation would connect to Alpaca API
    console.log('Connecting to Alpaca broker...');
    
    if (this.config.wsUrl) {
      this.ws = new WebSocket(this.config.wsUrl);
      this.setupWebSocketHandlers();
    }
  }

  async disconnect(): Promise<void> {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  async placeOrder(orderRequest: OrderRequest): Promise<Order> {
    this.validateOrder(orderRequest);
    
    return this.withRetry(async () => {
      // Implementation would call Alpaca API
      const response = await fetch(`${this.config.apiUrl}/orders`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderRequest),
      });
      
      if (!response.ok) {
        throw new Error(`Order placement failed: ${response.statusText}`);
      }
      
      return response.json();
    });
  }

  async cancelOrder(orderId: string): Promise<boolean> {
    return this.withRetry(async () => {
      const response = await fetch(`${this.config.apiUrl}/orders/${orderId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
      });
      
      return response.ok;
    });
  }

  async getOrder(orderId: string): Promise<Order> {
    return this.withRetry(async () => {
      const response = await fetch(`${this.config.apiUrl}/orders/${orderId}`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
      });
      
      if (!response.ok) {
        throw new Error(`Failed to get order: ${response.statusText}`);
      }
      
      return response.json();
    });
  }

  async getOrders(symbol?: string): Promise<Order[]> {
    return this.withRetry(async () => {
      const url = new URL(`${this.config.apiUrl}/orders`);
      if (symbol) {
        url.searchParams.set('symbols', symbol);
      }
      
      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
      });
      
      if (!response.ok) {
        throw new Error(`Failed to get orders: ${response.statusText}`);
      }
      
      return response.json();
    });
  }

  async getPositions(): Promise<Position[]> {
    return this.withRetry(async () => {
      const response = await fetch(`${this.config.apiUrl}/positions`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
      });
      
      if (!response.ok) {
        throw new Error(`Failed to get positions: ${response.statusText}`);
      }
      
      return response.json();
    });
  }

  async getBalances(): Promise<Balance[]> {
    return this.withRetry(async () => {
      const response = await fetch(`${this.config.apiUrl}/account`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
      });
      
      if (!response.ok) {
        throw new Error(`Failed to get balances: ${response.statusText}`);
      }
      
      const account = await response.json();
      return [
        {
          currency: 'USD',
          available: parseFloat(account.buying_power),
          locked: parseFloat(account.portfolio_value) - parseFloat(account.buying_power),
          total: parseFloat(account.portfolio_value),
        }
      ];
    });
  }

  subscribeToOrderUpdates(callback: (order: Order) => void): () => void {
    this.orderCallbacks.add(callback);
    return () => this.orderCallbacks.delete(callback);
  }

  subscribeToPositionUpdates(callback: (position: Position) => void): () => void {
    this.positionCallbacks.add(callback);
    return () => this.positionCallbacks.delete(callback);
  }

  private setupWebSocketHandlers(): void {
    if (!this.ws) return;

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'order_update') {
          this.orderCallbacks.forEach(callback => callback(data.order));
        } else if (data.type === 'position_update') {
          this.positionCallbacks.forEach(callback => callback(data.position));
        }
      } catch (error) {
        console.error('WebSocket message parsing error:', error);
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    this.ws.onclose = () => {
      console.log('WebSocket connection closed');
      // Implement reconnection logic here
    };
  }
}

// Broker factory
export class BrokerFactory {
  private static brokers: Map<string, typeof BrokerClient> = new Map();

  static register(brokerId: string, brokerClass: typeof BrokerClient): void {
    this.brokers.set(brokerId, brokerClass);
  }

  static create(config: BrokerConfig): BrokerClient {
    const BrokerClass = this.brokers.get(config.id);
    if (!BrokerClass) {
      throw new Error(`Unknown broker: ${config.id}`);
    }
    return new BrokerClass(config);
  }

  static getAvailableBrokers(): string[] {
    return Array.from(this.brokers.keys());
  }
}

// Register available brokers
BrokerFactory.register('alpaca', AlpacaBrokerClient);

// Secure broker service for server-side operations
export class BrokerService {
  private static instance: BrokerService;
  private brokerClients: Map<string, BrokerClient> = new Map();

  static getInstance(): BrokerService {
    if (!BrokerService.instance) {
      BrokerService.instance = new BrokerService();
    }
    return BrokerService.instance;
  }

  async initializeBroker(userId: string, config: BrokerConfig): Promise<void> {
    // Store broker config securely in database
    const supabase = await createClient();
    
    // Encrypt sensitive data before storing
    const encryptedConfig = await this.encryptBrokerConfig(config);
    
    const { error } = await supabase
      .from('user_broker_configs')
      .upsert({
        user_id: userId,
        broker_id: config.id,
        config: encryptedConfig,
        updated_at: new Date().toISOString(),
      });

    if (error) {
      throw new Error(`Failed to save broker config: ${error.message}`);
    }

    // Initialize broker client
    const client = BrokerFactory.create(config);
    await client.connect();
    this.brokerClients.set(`${userId}_${config.id}`, client);
  }

  async getBrokerClient(userId: string, brokerId: string): Promise<BrokerClient> {
    const clientKey = `${userId}_${brokerId}`;
    let client = this.brokerClients.get(clientKey);

    if (!client) {
      // Load config from database and initialize
      const config = await this.loadBrokerConfig(userId, brokerId);
      client = BrokerFactory.create(config);
      await client.connect();
      this.brokerClients.set(clientKey, client);
    }

    return client;
  }

  private async encryptBrokerConfig(config: BrokerConfig): Promise<string> {
    // Implement encryption logic here
    // For now, just JSON stringify (in production, use proper encryption)
    return JSON.stringify(config);
  }

  private async loadBrokerConfig(userId: string, brokerId: string): Promise<BrokerConfig> {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('user_broker_configs')
      .select('config')
      .eq('user_id', userId)
      .eq('broker_id', brokerId)
      .single();

    if (error || !data) {
      throw new Error(`Broker config not found for user ${userId} and broker ${brokerId}`);
    }

    // Decrypt and return config
    return JSON.parse(data.config);
  }
}
