// Monitoring and observability utilities
import { createClient } from './client';

export interface ErrorReport {
  id: string;
  userId?: string;
  error: {
    name: string;
    message: string;
    stack?: string;
  };
  context: {
    url: string;
    userAgent: string;
    timestamp: number;
    component?: string;
    action?: string;
    metadata?: Record<string, any>;
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface PerformanceMetric {
  id: string;
  userId?: string;
  metric: {
    name: string;
    value: number;
    unit: string;
  };
  context: {
    url: string;
    timestamp: number;
    component?: string;
    metadata?: Record<string, any>;
  };
}

export interface UserAction {
  id: string;
  userId?: string;
  action: {
    type: string;
    target: string;
    data?: Record<string, any>;
  };
  context: {
    url: string;
    timestamp: number;
    sessionId: string;
  };
}

class MonitoringService {
  private static instance: MonitoringService;
  private supabase = createClient();
  private sessionId: string;
  private userId?: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeErrorHandling();
    this.initializePerformanceMonitoring();
  }

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  setUserId(userId: string): void {
    this.userId = userId;
  }

  // Error reporting
  async reportError(
    error: Error,
    context: {
      component?: string;
      action?: string;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<void> {
    const errorReport: ErrorReport = {
      id: this.generateId(),
      userId: this.userId,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      context: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        ...context,
      },
      severity: this.determineSeverity(error, context),
    };

    try {
      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error reported:', errorReport);
      }

      // Send to Supabase
      await this.supabase.from('error_reports').insert(errorReport);

      // Send to external monitoring service (e.g., Sentry)
      if (typeof window !== 'undefined' && (window as any).Sentry) {
        (window as any).Sentry.captureException(error, {
          tags: {
            component: context.component,
            action: context.action,
          },
          extra: context.metadata,
        });
      }
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  // Performance monitoring
  async reportPerformanceMetric(
    name: string,
    value: number,
    unit: string,
    context: {
      component?: string;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<void> {
    const metric: PerformanceMetric = {
      id: this.generateId(),
      userId: this.userId,
      metric: { name, value, unit },
      context: {
        url: window.location.href,
        timestamp: Date.now(),
        ...context,
      },
    };

    try {
      await this.supabase.from('performance_metrics').insert(metric);
    } catch (error) {
      console.error('Failed to report performance metric:', error);
    }
  }

  // User action tracking
  async trackUserAction(
    type: string,
    target: string,
    data?: Record<string, any>
  ): Promise<void> {
    const action: UserAction = {
      id: this.generateId(),
      userId: this.userId,
      action: { type, target, data },
      context: {
        url: window.location.href,
        timestamp: Date.now(),
        sessionId: this.sessionId,
      },
    };

    try {
      await this.supabase.from('user_actions').insert(action);
    } catch (error) {
      console.error('Failed to track user action:', error);
    }
  }

  // Trading-specific monitoring
  async reportTradingEvent(
    event: {
      type: 'order_placed' | 'order_filled' | 'order_cancelled' | 'connection_lost' | 'data_delay';
      symbol?: string;
      orderId?: string;
      latency?: number;
      metadata?: Record<string, any>;
    }
  ): Promise<void> {
    await this.trackUserAction('trading_event', event.type, {
      symbol: event.symbol,
      orderId: event.orderId,
      latency: event.latency,
      ...event.metadata,
    });

    // Report critical trading events as high-priority metrics
    if (['connection_lost', 'data_delay'].includes(event.type)) {
      await this.reportPerformanceMetric(
        `trading_${event.type}`,
        event.latency || 0,
        'ms',
        {
          component: 'trading',
          metadata: event.metadata,
        }
      );
    }
  }

  private initializeErrorHandling(): void {
    if (typeof window === 'undefined') return;

    // Global error handler
    window.addEventListener('error', (event) => {
      this.reportError(event.error || new Error(event.message), {
        component: 'global',
        action: 'unhandled_error',
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        },
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError(
        event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
        {
          component: 'global',
          action: 'unhandled_promise_rejection',
        }
      );
    });
  }

  private initializePerformanceMonitoring(): void {
    if (typeof window === 'undefined') return;

    // Monitor page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          this.reportPerformanceMetric('page_load_time', navigation.loadEventEnd - navigation.fetchStart, 'ms');
          this.reportPerformanceMetric('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.fetchStart, 'ms');
          this.reportPerformanceMetric('first_byte', navigation.responseStart - navigation.fetchStart, 'ms');
        }
      }, 0);
    });

    // Monitor Core Web Vitals
    if ('web-vitals' in window) {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS((metric) => this.reportPerformanceMetric('cls', metric.value, 'score'));
        getFID((metric) => this.reportPerformanceMetric('fid', metric.value, 'ms'));
        getFCP((metric) => this.reportPerformanceMetric('fcp', metric.value, 'ms'));
        getLCP((metric) => this.reportPerformanceMetric('lcp', metric.value, 'ms'));
        getTTFB((metric) => this.reportPerformanceMetric('ttfb', metric.value, 'ms'));
      });
    }
  }

  private determineSeverity(error: Error, context: any): ErrorReport['severity'] {
    // Trading-related errors are critical
    if (context.component === 'trading' || context.component === 'broker') {
      return 'critical';
    }

    // Chart rendering errors are high priority
    if (context.component === 'chart') {
      return 'high';
    }

    // Authentication errors are medium priority
    if (context.component === 'auth') {
      return 'medium';
    }

    // Network errors are medium priority
    if (error.name === 'NetworkError' || error.message.includes('fetch')) {
      return 'medium';
    }

    return 'low';
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const monitoring = MonitoringService.getInstance();

// React hook for easy integration
export function useMonitoring() {
  return {
    reportError: monitoring.reportError.bind(monitoring),
    reportPerformanceMetric: monitoring.reportPerformanceMetric.bind(monitoring),
    trackUserAction: monitoring.trackUserAction.bind(monitoring),
    reportTradingEvent: monitoring.reportTradingEvent.bind(monitoring),
  };
}

// Higher-order component for error boundaries
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) {
  return class ErrorBoundary extends React.Component<P, { hasError: boolean }> {
    constructor(props: P) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError(): { hasError: boolean } {
      return { hasError: true };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      monitoring.reportError(error, {
        component: componentName,
        action: 'component_error',
        metadata: {
          componentStack: errorInfo.componentStack,
        },
      });
    }

    render() {
      if (this.state.hasError) {
        return (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <h3 className="text-red-800 font-medium">Something went wrong</h3>
            <p className="text-red-600 text-sm mt-1">
              An error occurred in the {componentName} component. Please refresh the page.
            </p>
          </div>
        );
      }

      return <Component {...this.props} />;
    }
  };
}
