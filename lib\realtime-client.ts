"use client";

import { createClient } from './client';
import { RealtimeChannel } from '@supabase/supabase-js';
import { useState, useEffect } from 'react';

export interface PriceUpdate {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: number;
}

export interface OrderBookUpdate {
  symbol: string;
  bids: Array<[number, number]>; // [price, quantity]
  asks: Array<[number, number]>;
  timestamp: number;
}

export interface TradeUpdate {
  symbol: string;
  price: number;
  quantity: number;
  side: 'buy' | 'sell';
  timestamp: number;
}

export class RealtimeDataManager {
  private static instance: RealtimeDataManager;
  private supabase = createClient();
  private channels: Map<string, RealtimeChannel> = new Map();
  private subscribers: Map<string, Set<(data: any) => void>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  static getInstance(): RealtimeDataManager {
    if (!RealtimeDataManager.instance) {
      RealtimeDataManager.instance = new RealtimeDataManager();
    }
    return RealtimeDataManager.instance;
  }

  // Subscribe to price updates for a symbol
  subscribeToPriceUpdates(symbol: string, callback: (update: PriceUpdate) => void): () => void {
    const channelName = `price_updates_${symbol}`;
    
    if (!this.channels.has(channelName)) {
      const channel = this.supabase
        .channel(channelName)
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'price_updates',
          filter: `symbol=eq.${symbol}`
        }, (payload) => {
          const update: PriceUpdate = payload.new as PriceUpdate;
          this.notifySubscribers(channelName, update);
        })
        .on('presence', { event: 'sync' }, () => {
          console.log('Price updates channel synced for', symbol);
        })
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log(`Subscribed to price updates for ${symbol}`);
            this.reconnectAttempts = 0;
          } else if (status === 'CHANNEL_ERROR') {
            this.handleReconnection(channelName);
          }
        });

      this.channels.set(channelName, channel);
      this.subscribers.set(channelName, new Set());
    }

    // Add callback to subscribers
    const channelSubscribers = this.subscribers.get(channelName)!;
    channelSubscribers.add(callback);

    // Return unsubscribe function
    return () => {
      channelSubscribers.delete(callback);
      if (channelSubscribers.size === 0) {
        this.unsubscribeChannel(channelName);
      }
    };
  }

  // Subscribe to order book updates
  subscribeToOrderBook(symbol: string, callback: (update: OrderBookUpdate) => void): () => void {
    const channelName = `orderbook_${symbol}`;
    
    if (!this.channels.has(channelName)) {
      const channel = this.supabase
        .channel(channelName)
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'order_book',
          filter: `symbol=eq.${symbol}`
        }, (payload) => {
          const update: OrderBookUpdate = payload.new as OrderBookUpdate;
          this.notifySubscribers(channelName, update);
        })
        .subscribe();

      this.channels.set(channelName, channel);
      this.subscribers.set(channelName, new Set());
    }

    const channelSubscribers = this.subscribers.get(channelName)!;
    channelSubscribers.add(callback);

    return () => {
      channelSubscribers.delete(callback);
      if (channelSubscribers.size === 0) {
        this.unsubscribeChannel(channelName);
      }
    };
  }

  // Subscribe to trade updates
  subscribeToTrades(symbol: string, callback: (update: TradeUpdate) => void): () => void {
    const channelName = `trades_${symbol}`;
    
    if (!this.channels.has(channelName)) {
      const channel = this.supabase
        .channel(channelName)
        .on('postgres_changes', {
          event: 'INSERT',
          schema: 'public',
          table: 'trades',
          filter: `symbol=eq.${symbol}`
        }, (payload) => {
          const update: TradeUpdate = payload.new as TradeUpdate;
          this.notifySubscribers(channelName, update);
        })
        .subscribe();

      this.channels.set(channelName, channel);
      this.subscribers.set(channelName, new Set());
    }

    const channelSubscribers = this.subscribers.get(channelName)!;
    channelSubscribers.add(callback);

    return () => {
      channelSubscribers.delete(callback);
      if (channelSubscribers.size === 0) {
        this.unsubscribeChannel(channelName);
      }
    };
  }

  private notifySubscribers(channelName: string, data: any): void {
    const subscribers = this.subscribers.get(channelName);
    if (subscribers) {
      subscribers.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in realtime callback:', error);
        }
      });
    }
  }

  private unsubscribeChannel(channelName: string): void {
    const channel = this.channels.get(channelName);
    if (channel) {
      this.supabase.removeChannel(channel);
      this.channels.delete(channelName);
      this.subscribers.delete(channelName);
    }
  }

  private handleReconnection(channelName: string): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      setTimeout(() => {
        console.log(`Attempting to reconnect channel ${channelName} (attempt ${this.reconnectAttempts})`);
        this.unsubscribeChannel(channelName);
        // Re-establish subscriptions would need to be handled by the calling component
      }, delay);
    } else {
      console.error(`Max reconnection attempts reached for channel ${channelName}`);
    }
  }

  // Cleanup all subscriptions
  cleanup(): void {
    this.channels.forEach((channel, channelName) => {
      this.unsubscribeChannel(channelName);
    });
    this.reconnectAttempts = 0;
  }
}

// React hooks for easy integration
export function useRealtimePrices(symbol: string) {
  const [priceData, setPriceData] = useState<PriceUpdate | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const manager = RealtimeDataManager.getInstance();
    setIsConnected(true);

    const unsubscribe = manager.subscribeToPriceUpdates(symbol, (update) => {
      setPriceData(update);
    });

    return () => {
      unsubscribe();
      setIsConnected(false);
    };
  }, [symbol]);

  return { priceData, isConnected };
}

export function useRealtimeOrderBook(symbol: string) {
  const [orderBook, setOrderBook] = useState<OrderBookUpdate | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const manager = RealtimeDataManager.getInstance();
    setIsConnected(true);

    const unsubscribe = manager.subscribeToOrderBook(symbol, (update) => {
      setOrderBook(update);
    });

    return () => {
      unsubscribe();
      setIsConnected(false);
    };
  }, [symbol]);

  return { orderBook, isConnected };
}
