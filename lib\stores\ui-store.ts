import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// UI State interfaces
export interface Modal {
  id: string;
  type: 'order' | 'settings' | 'broker-config' | 'strategy' | 'alert';
  isOpen: boolean;
  data?: any;
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: number;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

export interface ChartSettings {
  theme: 'light' | 'dark';
  timeframe: string;
  symbol: string;
  showVolume: boolean;
  showGrid: boolean;
  crosshairMode: boolean;
  autoScale: boolean;
  indicators: string[];
}

export interface LayoutSettings {
  sidebarCollapsed: boolean;
  rightPanelCollapsed: boolean;
  bottomPanelHeight: number;
  chartHeight: number;
  showOrderBook: boolean;
  showTrades: boolean;
  showPositions: boolean;
}

interface UIState {
  // Modal management
  modals: Modal[];
  openModal: (modal: Omit<Modal, 'isOpen'>) => void;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
  
  // Notification management
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // Chart settings
  chartSettings: ChartSettings;
  updateChartSettings: (settings: Partial<ChartSettings>) => void;
  
  // Layout settings
  layoutSettings: LayoutSettings;
  updateLayoutSettings: (settings: Partial<LayoutSettings>) => void;
  
  // Loading states
  loadingStates: Record<string, boolean>;
  setLoading: (key: string, loading: boolean) => void;
  
  // Connection status
  connectionStatus: {
    broker: 'connected' | 'disconnected' | 'connecting' | 'error';
    realtime: 'connected' | 'disconnected' | 'connecting' | 'error';
    lastUpdate: number;
  };
  updateConnectionStatus: (status: Partial<UIState['connectionStatus']>) => void;
  
  // Active selections
  activeSymbol: string;
  activeTimeframe: string;
  setActiveSymbol: (symbol: string) => void;
  setActiveTimeframe: (timeframe: string) => void;
  
  // Keyboard shortcuts
  keyboardShortcuts: Record<string, () => void>;
  registerShortcut: (key: string, action: () => void) => void;
  unregisterShortcut: (key: string) => void;
}

export const useUIStore = create<UIState>()(
  devtools(
    persist(
      (set, get) => ({
        // Modal management
        modals: [],
        openModal: (modal) => set((state) => ({
          modals: [...state.modals.filter(m => m.id !== modal.id), { ...modal, isOpen: true }]
        })),
        closeModal: (id) => set((state) => ({
          modals: state.modals.map(m => m.id === id ? { ...m, isOpen: false } : m)
        })),
        closeAllModals: () => set((state) => ({
          modals: state.modals.map(m => ({ ...m, isOpen: false }))
        })),
        
        // Notification management
        notifications: [],
        addNotification: (notification) => {
          const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          const newNotification: Notification = {
            ...notification,
            id,
            timestamp: Date.now(),
          };
          
          set((state) => ({
            notifications: [...state.notifications, newNotification]
          }));
          
          // Auto-remove notification after duration
          if (notification.duration !== 0) {
            const duration = notification.duration || 5000;
            setTimeout(() => {
              get().removeNotification(id);
            }, duration);
          }
        },
        removeNotification: (id) => set((state) => ({
          notifications: state.notifications.filter(n => n.id !== id)
        })),
        clearNotifications: () => set({ notifications: [] }),
        
        // Chart settings
        chartSettings: {
          theme: 'dark',
          timeframe: '1H',
          symbol: 'NIFTY',
          showVolume: true,
          showGrid: true,
          crosshairMode: false,
          autoScale: true,
          indicators: [],
        },
        updateChartSettings: (settings) => set((state) => ({
          chartSettings: { ...state.chartSettings, ...settings }
        })),
        
        // Layout settings
        layoutSettings: {
          sidebarCollapsed: false,
          rightPanelCollapsed: false,
          bottomPanelHeight: 300,
          chartHeight: 600,
          showOrderBook: true,
          showTrades: true,
          showPositions: true,
        },
        updateLayoutSettings: (settings) => set((state) => ({
          layoutSettings: { ...state.layoutSettings, ...settings }
        })),
        
        // Loading states
        loadingStates: {},
        setLoading: (key, loading) => set((state) => ({
          loadingStates: { ...state.loadingStates, [key]: loading }
        })),
        
        // Connection status
        connectionStatus: {
          broker: 'disconnected',
          realtime: 'disconnected',
          lastUpdate: Date.now(),
        },
        updateConnectionStatus: (status) => set((state) => ({
          connectionStatus: { 
            ...state.connectionStatus, 
            ...status, 
            lastUpdate: Date.now() 
          }
        })),
        
        // Active selections
        activeSymbol: 'NIFTY',
        activeTimeframe: '1H',
        setActiveSymbol: (symbol) => set({ activeSymbol: symbol }),
        setActiveTimeframe: (timeframe) => set({ activeTimeframe: timeframe }),
        
        // Keyboard shortcuts
        keyboardShortcuts: {},
        registerShortcut: (key, action) => set((state) => ({
          keyboardShortcuts: { ...state.keyboardShortcuts, [key]: action }
        })),
        unregisterShortcut: (key) => set((state) => {
          const { [key]: removed, ...rest } = state.keyboardShortcuts;
          return { keyboardShortcuts: rest };
        }),
      }),
      {
        name: 'trading-ui-store',
        partialize: (state) => ({
          chartSettings: state.chartSettings,
          layoutSettings: state.layoutSettings,
          activeSymbol: state.activeSymbol,
          activeTimeframe: state.activeTimeframe,
        }),
      }
    ),
    {
      name: 'trading-ui-store',
    }
  )
);

// Selectors for better performance
export const useModals = () => useUIStore((state) => state.modals);
export const useNotifications = () => useUIStore((state) => state.notifications);
export const useChartSettings = () => useUIStore((state) => state.chartSettings);
export const useLayoutSettings = () => useUIStore((state) => state.layoutSettings);
export const useConnectionStatus = () => useUIStore((state) => state.connectionStatus);
export const useActiveSymbol = () => useUIStore((state) => state.activeSymbol);
export const useActiveTimeframe = () => useUIStore((state) => state.activeTimeframe);
export const useLoadingState = (key: string) => useUIStore((state) => state.loadingStates[key] || false);

// Action selectors
export const useUIActions = () => useUIStore((state) => ({
  openModal: state.openModal,
  closeModal: state.closeModal,
  closeAllModals: state.closeAllModals,
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  clearNotifications: state.clearNotifications,
  updateChartSettings: state.updateChartSettings,
  updateLayoutSettings: state.updateLayoutSettings,
  setLoading: state.setLoading,
  updateConnectionStatus: state.updateConnectionStatus,
  setActiveSymbol: state.setActiveSymbol,
  setActiveTimeframe: state.setActiveTimeframe,
  registerShortcut: state.registerShortcut,
  unregisterShortcut: state.unregisterShortcut,
}));

// Utility hooks
export const useModal = (id: string) => {
  const modal = useUIStore((state) => state.modals.find(m => m.id === id));
  const { openModal, closeModal } = useUIActions();
  
  return {
    isOpen: modal?.isOpen || false,
    data: modal?.data,
    open: (data?: any) => openModal({ id, type: modal?.type || 'settings', data }),
    close: () => closeModal(id),
  };
};

export const useNotificationActions = () => {
  const { addNotification } = useUIActions();
  
  return {
    success: (title: string, message: string, duration?: number) =>
      addNotification({ type: 'success', title, message, duration }),
    error: (title: string, message: string, duration?: number) =>
      addNotification({ type: 'error', title, message, duration }),
    warning: (title: string, message: string, duration?: number) =>
      addNotification({ type: 'warning', title, message, duration }),
    info: (title: string, message: string, duration?: number) =>
      addNotification({ type: 'info', title, message, duration }),
  };
};
